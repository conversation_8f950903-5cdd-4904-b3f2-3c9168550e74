# 高级路径追踪系统使用指南

## 🎯 功能概述

本系统实现了针对"任意组合连续形状（含左右往复）的关键坐标点识别"，完全按照用户提供的5步骤方法论开发：

### ✅ 已实现的核心功能

1. **智能图像预处理**
   - 灰度化 → 去噪（高斯模糊+中值滤波）
   - 二值化（固定阈值+自适应阈值自动选择）
   - 形态学操作确保轮廓连续性

2. **连续轮廓提取**
   - 使用 `CHAIN_APPROX_NONE` 保留所有轮廓点
   - 按面积和长度筛选主轮廓
   - 保持轮廓自然连续顺序（"一笔画"特性）

3. **关键特征点检测**
   - **拐点检测**：前后k个邻点拟合直线求夹角，超过阈值为拐点
   - **形状切换点检测**：滑动窗口拟合误差分析，检测直线↔圆弧切换点

4. **智能分段与端点提取**
   - 以特征突变点为界分段
   - 自动判断每段的形状类型（直线/圆弧）
   - 提取各段的起点、终点

5. **激光控制数据输出**
   - **直线段**：起点、终点、长度、角度
   - **圆弧段**：起点、终点、中点、圆心、半径

## 🚀 使用方法

### 基础版本
```bash
python path_tracker_test.py
```

### 高级版本（推荐）
```bash
python advanced_path_tracker.py
```

## 🎮 控制键说明

- **`q`**：退出程序
- **`s`**：保存当前帧
- **`p`**：显示参数调整菜单
- **`r`**：重置所有参数到默认值

## 🔧 关键参数说明

### 可调参数
```python
curvature_threshold = 30    # 曲率阈值（角度）- 控制拐点敏感度
curvature_window = 5        # 曲率计算窗口 - 影响拐点检测精度
sliding_window_size = 20    # 滑动窗口大小 - 影响形状切换点检测
min_segment_length = 15     # 最小段长度 - 过滤过短的段
min_feature_distance = 10   # 特征点最小距离 - 避免特征点过密
```

### 参数调优建议

1. **线条较粗或图像较大**：
   - 增大 `curvature_window` (7-10)
   - 增大 `sliding_window_size` (30-40)

2. **线条较细或需要精细检测**：
   - 减小 `curvature_window` (3-5)
   - 减小 `sliding_window_size` (15-25)

3. **拐点检测过于敏感**：
   - 增大 `curvature_threshold` (40-60)

4. **拐点检测不够敏感**：
   - 减小 `curvature_threshold` (15-25)

## 📊 输出数据格式

### 激光控制数据示例

```python
# 直线段
{
    'segment_id': 0,
    'type': 'line',
    'start': (100, 200),      # 起点坐标
    'end': (300, 200),        # 终点坐标
    'length': 200.0,          # 长度
    'angle': 0.0              # 角度（度）
}

# 圆弧段
{
    'segment_id': 1,
    'type': 'arc',
    'start': (300, 200),      # 起点坐标
    'end': (400, 300),        # 终点坐标
    'mid': (350, 250),        # 中点坐标
    'center': (350, 200),     # 圆心坐标
    'radius': 70.7            # 半径
}
```

## 🎨 可视化说明

### 颜色编码
- **灰色细线**：原始轮廓
- **蓝色粗线**：直线段
- **黄色粗线**：圆弧段
- **红色圆点**：段的起点/终点
- **紫色圆点**：圆弧中点
- **绿色圆点**：圆心
- **黄色圆圈**：拐点（曲率突变点）
- **紫色圆圈**：形状切换点

### 标注说明
- **L0, L1, ...**：直线段编号
- **A0, A1, ...**：圆弧段编号
- **C**：拐点标记
- **S**：形状切换点标记

## 🧪 测试建议

### 1. 简单测试
- 画一条直线 → 检查起点终点识别
- 画一个半圆 → 检查圆心半径计算

### 2. 组合测试
- 直线+半圆 → 检查连接点识别
- 左右往复移动 → 检查方向变化点

### 3. 复杂测试
- 多段组合路径
- 不规则曲线
- 带噪声的图像

## ⚠️ 注意事项

1. **图像要求**：
   - 白底黑线效果最佳
   - 线条应连续，避免断裂
   - 避免过多噪点

2. **环境要求**：
   - 光照均匀
   - 避免阴影干扰
   - 摄像头稳定

3. **参数调整**：
   - 根据实际线条粗细调整窗口大小
   - 根据检测精度要求调整阈值
   - 建议先用默认参数测试，再根据结果调优

## 🔍 故障排除

### 问题1：检测不到轮廓
- 检查二值化效果（右上角小窗口）
- 调整阈值或使用自适应阈值
- 确保线条连续性

### 问题2：拐点检测过多/过少
- 调整 `curvature_threshold` 参数
- 调整 `curvature_window` 大小

### 问题3：分段不准确
- 调整 `sliding_window_size` 参数
- 检查 `min_segment_length` 设置

### 问题4：特征点过密
- 增大 `min_feature_distance` 参数

## 📈 性能优化

1. **实时性优化**：
   - 降低图像分辨率
   - 减小滑动窗口大小
   - 增大最小段长度

2. **精度优化**：
   - 增大曲率计算窗口
   - 减小特征点距离阈值
   - 使用更精细的拟合算法

## 🎯 应用场景

- **激光雕刻/切割**：路径规划和控制点生成
- **机器人路径规划**：轨迹关键点提取
- **图像分析**：形状特征提取
- **工业检测**：轮廓分析和测量

---

**开发完成度**：✅ 100%
**测试状态**：🧪 待用户验证
**优化空间**：🔧 参数微调和特定场景适配
