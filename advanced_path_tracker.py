import cv2
import numpy as np
import math

# === 可调参数 ===
class PathTrackerParams:
    def __init__(self):
        # 曲率检测参数
        self.curvature_threshold = 30  # 曲率阈值（角度）
        self.curvature_window = 5      # 曲率计算窗口大小
        
        # 滑动窗口参数
        self.sliding_window_size = 20  # 滑动窗口大小
        self.min_segment_length = 15   # 最小段长度
        
        # 特征点过滤参数
        self.min_feature_distance = 10  # 特征点最小距离
        
        # 轮廓筛选参数
        self.min_contour_length = 30   # 最小轮廓长度
        self.min_contour_area = 100    # 最小轮廓面积

params = PathTrackerParams()

def calculate_curvature_advanced(points, index, k=None):
    """改进的曲率计算：前后k个邻点拟合直线求夹角"""
    if k is None:
        k = params.curvature_window
        
    if len(points) < k * 2 + 1 or index < k or index >= len(points) - k:
        return 0
    
    try:
        # 取前后k个邻点
        p_prev = points[index-k:index]
        p_next = points[index:index+k]
        
        # 拟合前段直线
        x_prev = np.array([p[0] for p in p_prev], dtype=float)
        y_prev = np.array([p[1] for p in p_prev], dtype=float)
        A_prev = np.vstack([x_prev, np.ones(len(x_prev))]).T
        coeffs_prev = np.linalg.lstsq(A_prev, y_prev, rcond=None)[0]
        a1 = coeffs_prev[0]
        
        # 拟合后段直线
        x_next = np.array([p[0] for p in p_next], dtype=float)
        y_next = np.array([p[1] for p in p_next], dtype=float)
        A_next = np.vstack([x_next, np.ones(len(x_next))]).T
        coeffs_next = np.linalg.lstsq(A_next, y_next, rcond=None)[0]
        a2 = coeffs_next[0]
        
        # 计算两直线夹角
        v1 = np.array([1, a1])
        v2 = np.array([1, a2])
        cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
        cos_angle = np.clip(cos_angle, -1, 1)
        angle = np.arccos(cos_angle)
        
        return angle
    except:
        return 0

def fit_line_error(points):
    """计算点集拟合直线的平均误差"""
    if len(points) < 2:
        return float('inf')
    
    try:
        x = np.array([p[0] for p in points], dtype=float)
        y = np.array([p[1] for p in points], dtype=float)
        A = np.vstack([x, np.ones(len(x))]).T
        coeffs = np.linalg.lstsq(A, y, rcond=None)[0]
        a, b = coeffs
        distances = np.abs(a * x - y + b) / np.sqrt(a**2 + 1)
        return np.mean(distances)
    except:
        return float('inf')

def fit_circle_error(points):
    """计算点集拟合圆的平均误差"""
    if len(points) < 3:
        return float('inf')
    
    try:
        x = np.array([p[0] for p in points], dtype=float)
        y = np.array([p[1] for p in points], dtype=float)
        A = np.column_stack([x, y, np.ones(len(x))])
        b = -(x**2 + y**2)
        coeffs = np.linalg.lstsq(A, b, rcond=None)[0]
        D, E, F = coeffs
        center_x = -D / 2
        center_y = -E / 2
        radius = np.sqrt(center_x**2 + center_y**2 - F)
        distances_to_center = np.sqrt((x - center_x)**2 + (y - center_y)**2)
        errors = np.abs(distances_to_center - radius)
        return np.mean(errors)
    except:
        return float('inf')

def detect_feature_points(contour):
    """检测关键特征点：拐点 + 形状切换点"""
    if len(contour) < params.sliding_window_size * 2:
        return []
    
    feature_points = []
    
    # 1. 检测拐点（曲率突变点）
    curvatures = []
    for i in range(len(contour)):
        curvature = calculate_curvature_advanced(contour, i)
        curvatures.append(math.degrees(curvature))
    
    for i in range(1, len(curvatures) - 1):
        if curvatures[i] > params.curvature_threshold:
            feature_points.append({
                'index': i,
                'type': 'curvature_point',
                'value': curvatures[i],
                'point': tuple(contour[i])
            })
    
    # 2. 检测形状切换点（滑动窗口拟合误差分析）
    shape_types = []
    step = params.sliding_window_size // 2
    
    for i in range(0, len(contour) - params.sliding_window_size, step):
        window_points = contour[i:i + params.sliding_window_size]
        line_error = fit_line_error(window_points)
        circle_error = fit_circle_error(window_points)
        
        if line_error < circle_error:
            shape_types.append(('line', i))
        else:
            shape_types.append(('circle', i))
    
    # 检测形状切换点
    for i in range(1, len(shape_types)):
        prev_shape, prev_idx = shape_types[i-1]
        curr_shape, curr_idx = shape_types[i]
        
        if prev_shape != curr_shape:
            switch_idx = (prev_idx + curr_idx) // 2
            if switch_idx < len(contour):
                feature_points.append({
                    'index': switch_idx,
                    'type': 'shape_switch_point',
                    'value': f"{prev_shape}->{curr_shape}",
                    'point': tuple(contour[switch_idx])
                })
    
    # 按索引排序并去重
    feature_points.sort(key=lambda x: x['index'])
    
    # 去除距离过近的特征点
    filtered_points = []
    for point in feature_points:
        if not filtered_points:
            filtered_points.append(point)
        else:
            last_point = filtered_points[-1]
            distance = np.sqrt((point['point'][0] - last_point['point'][0])**2 + 
                             (point['point'][1] - last_point['point'][1])**2)
            if distance > params.min_feature_distance:
                filtered_points.append(point)
    
    return filtered_points

def extract_arc_keypoints(points):
    """提取弧线段的关键点"""
    if len(points) < 5:
        return None
    
    try:
        # 三点法拟合圆
        p1 = np.array(points[0], dtype=float)
        p2 = np.array(points[len(points)//2], dtype=float)
        p3 = np.array(points[-1], dtype=float)
        
        ax, ay = p1
        bx, by = p2
        cx, cy = p3
        
        d = 2 * (ax * (by - cy) + bx * (cy - ay) + cx * (ay - by))
        if abs(d) < 1e-6:
            return None
        
        ux = ((ax*ax + ay*ay) * (by - cy) + (bx*bx + by*by) * (cy - ay) + (cx*cx + cy*cy) * (ay - by)) / d
        uy = ((ax*ax + ay*ay) * (cx - bx) + (bx*bx + by*by) * (ax - cx) + (cx*cx + cy*cy) * (bx - ax)) / d
        
        center = (int(ux), int(uy))
        radius = np.sqrt((ux - ax)**2 + (uy - ay)**2)
        
        if radius < 10 or radius > 200:
            return None
        
        return {
            'type': 'arc',
            'start': tuple(map(int, points[0])),
            'end': tuple(map(int, points[-1])),
            'mid': tuple(map(int, points[len(points)//2])),
            'center': center,
            'radius': radius
        }
    except:
        return None

def process_frame(img):
    """处理单帧图像，返回激光控制数据"""
    # 图像预处理
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    denoised = cv2.medianBlur(blurred, 3)
    
    # 二值化
    _, binary_fixed = cv2.threshold(denoised, 127, 255, cv2.THRESH_BINARY_INV)
    binary_adaptive = cv2.adaptiveThreshold(denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                          cv2.THRESH_BINARY_INV, 11, 2)
    
    # 选择更好的二值化结果
    contours_fixed, _ = cv2.findContours(binary_fixed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    contours_adaptive, _ = cv2.findContours(binary_adaptive, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if len(contours_fixed) > 0 and len(contours_adaptive) > 0:
        max_len_fixed = max(len(c) for c in contours_fixed)
        max_len_adaptive = max(len(c) for c in contours_adaptive)
        if max_len_fixed > max_len_adaptive * 0.8 and len(contours_fixed) <= len(contours_adaptive):
            binary = binary_fixed
        else:
            binary = binary_adaptive
    else:
        binary = binary_fixed
    
    # 形态学操作
    kernel = np.ones((3, 3), np.uint8)
    binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
    binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)
    
    # 提取主轮廓
    contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)
    
    if not contours:
        return None, binary
    
    # 选择最大轮廓
    contours_with_area = [(cv2.contourArea(c), c) for c in contours]
    contours_with_area.sort(key=lambda x: x[0], reverse=True)
    
    main_contour = None
    for area, contour in contours_with_area:
        if len(contour) > params.min_contour_length and area > params.min_contour_area:
            main_contour = contour.reshape(-1, 2)
            break
    
    if main_contour is None:
        return None, binary
    
    # 检测特征点和分段
    feature_points = detect_feature_points(main_contour)
    
    # 构建关键点列表
    key_points = [{'index': 0, 'type': 'start_point', 'point': tuple(main_contour[0])}]
    key_points.extend(feature_points)
    key_points.append({'index': len(main_contour)-1, 'type': 'end_point', 'point': tuple(main_contour[-1])})
    key_points.sort(key=lambda x: x['index'])
    
    # 分段处理
    laser_control_data = []
    segments = []
    
    for i in range(len(key_points) - 1):
        start_idx = key_points[i]['index']
        end_idx = key_points[i + 1]['index']
        
        if end_idx > start_idx and end_idx - start_idx > params.min_segment_length:
            segment_points = main_contour[start_idx:end_idx + 1]
            
            # 判断形状类型
            line_error = fit_line_error(segment_points)
            circle_error = fit_circle_error(segment_points)
            shape_type = 'line' if line_error < circle_error else 'arc'
            
            start_point = tuple(main_contour[start_idx])
            end_point = tuple(main_contour[end_idx])
            
            if shape_type == 'line':
                length = np.sqrt((end_point[0] - start_point[0])**2 + (end_point[1] - start_point[1])**2)
                angle = math.degrees(math.atan2(end_point[1] - start_point[1], end_point[0] - start_point[0]))
                
                laser_data = {
                    'segment_id': len(laser_control_data),
                    'type': 'line',
                    'start': start_point,
                    'end': end_point,
                    'length': length,
                    'angle': angle
                }
            else:
                arc_keypoints = extract_arc_keypoints(segment_points)
                if arc_keypoints:
                    laser_data = {
                        'segment_id': len(laser_control_data),
                        'type': 'arc',
                        'start': start_point,
                        'end': end_point,
                        'mid': arc_keypoints['mid'],
                        'center': arc_keypoints['center'],
                        'radius': arc_keypoints['radius']
                    }
                else:
                    continue
            
            laser_control_data.append(laser_data)
            segments.append({
                'points': segment_points,
                'type': shape_type,
                'data': laser_data
            })
    
    return {
        'contour': main_contour,
        'feature_points': feature_points,
        'segments': segments,
        'laser_data': laser_control_data
    }, binary

# 参数调整函数
def adjust_params():
    """实时参数调整"""
    print("\n=== 参数调整 ===")
    print("当前参数:")
    print(f"1. 曲率阈值: {params.curvature_threshold}°")
    print(f"2. 曲率窗口: {params.curvature_window}")
    print(f"3. 滑动窗口: {params.sliding_window_size}")
    print(f"4. 最小段长度: {params.min_segment_length}")
    print(f"5. 特征点距离: {params.min_feature_distance}")
    print("按 1-5 调整参数，按其他键继续")

if __name__ == "__main__":
    # 初始化摄像头
    cap = cv2.VideoCapture(0)
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    
    print("高级路径追踪程序已启动...")
    print("按键说明:")
    print("  'q': 退出程序")
    print("  's': 保存当前帧")
    print("  'p': 调整参数")
    print("  'r': 重置参数")
    
    frame_count = 0
    
    while True:
        frame_count += 1
        
        ret, img = cap.read()
        if not ret:
            print("无法读取摄像头画面")
            break
        
        img_display = img.copy()
        height, width = img.shape[:2]
        
        # 处理帧
        result, binary = process_frame(img)
        
        if result:
            # 可视化结果
            contour = result['contour']
            feature_points = result['feature_points']
            segments = result['segments']
            laser_data = result['laser_data']
            
            # 绘制轮廓
            cv2.drawContours(img_display, [contour.reshape(-1,1,2)], -1, (128, 128, 128), 1)
            
            # 绘制段落
            for i, segment in enumerate(segments):
                data = segment['data']
                if data['type'] == 'line':
                    cv2.line(img_display, data['start'], data['end'], (255, 0, 0), 3)
                    cv2.circle(img_display, data['start'], 6, (0, 0, 255), -1)
                    cv2.circle(img_display, data['end'], 6, (0, 0, 255), -1)
                    
                    mid_x = (data['start'][0] + data['end'][0]) // 2
                    mid_y = (data['start'][1] + data['end'][1]) // 2
                    cv2.putText(img_display, f"L{i}", (mid_x, mid_y-10), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)
                
                elif data['type'] == 'arc':
                    points = np.array(segment['points'], np.int32)
                    cv2.polylines(img_display, [points], False, (0, 255, 255), 3)
                    cv2.circle(img_display, data['start'], 6, (0, 0, 255), -1)
                    cv2.circle(img_display, data['end'], 6, (0, 0, 255), -1)
                    cv2.circle(img_display, data['mid'], 6, (255, 0, 255), -1)
                    cv2.circle(img_display, data['center'], 4, (0, 255, 0), -1)
                    
                    cv2.putText(img_display, f"A{i}", (data['mid'][0], data['mid'][1]-10), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)
            
            # 绘制特征点
            for fp in feature_points:
                point = fp['point']
                if fp['type'] == 'curvature_point':
                    cv2.circle(img_display, point, 8, (255, 255, 0), 2)
                elif fp['type'] == 'shape_switch_point':
                    cv2.circle(img_display, point, 8, (255, 0, 255), 2)
            
            # 显示信息
            cv2.putText(img_display, f"Frame: {frame_count}", (10, 20), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            cv2.putText(img_display, f"Segments: {len(segments)}", (10, 40), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            cv2.putText(img_display, f"Features: {len(feature_points)}", (10, 60), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # 显示二值图像（小窗口）
        binary_small = cv2.resize(binary, (160, 120))
        img_display[10:130, width-170:width-10] = cv2.cvtColor(binary_small, cv2.COLOR_GRAY2BGR)
        
        cv2.imshow('Advanced Path Tracker', img_display)
        
        # 按键处理
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break
        elif key == ord('s'):
            cv2.imwrite(f'advanced_path_frame_{frame_count}.jpg', img_display)
            print(f"保存帧 {frame_count}")
        elif key == ord('p'):
            adjust_params()
        elif key == ord('r'):
            params = PathTrackerParams()
            print("参数已重置")
        elif key >= ord('1') and key <= ord('5'):
            param_idx = key - ord('1')
            if param_idx == 0:
                params.curvature_threshold = max(5, params.curvature_threshold - 5) if cv2.waitKey(1) & 0xFF == ord('-') else params.curvature_threshold + 5
            elif param_idx == 1:
                params.curvature_window = max(3, params.curvature_window - 1) if cv2.waitKey(1) & 0xFF == ord('-') else params.curvature_window + 1
            # 可以继续添加其他参数调整
    
    cap.release()
    cv2.destroyAllWindows()
