import cv2
import numpy as np
import time

# 初始化摄像头 (0为默认摄像头)
cap = cv2.VideoCapture(0)
cap.set(cv2.CAP_PROP_FRAME_WIDTH, 320)
cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 240)

# 定义棋盘的最小面积限制，用于筛选有效的棋盘轮廓
MIN_BOARD_AREA = 5000
# 定义棋子的最大半径限制，用于霍夫圆检测
MAX_PIECE_RADIUS = 20
# 定义多识别次数，用于累积圆信息，提高识别准确性
DETECT_TIMES = 5
# 初始化累积的圆信息列表
accumulated_circles = []

print("按 'q' 键退出程序")

# 主循环，持续运行直到用户按下退出键
while True:
    # 从摄像头读取一帧图像
    ret, img = cap.read()
    if not ret:
        print("无法读取摄像头数据")
        break

    # 记录开始时间
    start_time = time.time()

    # 将图像转换为灰度图像
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    # 对灰度图像进行高斯模糊，减少噪声
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    # 使用 Canny 边缘检测算法检测图像的边缘
    edged = cv2.Canny(blurred, 50, 150)

    # 查找图像中的轮廓
    contours, _ = cv2.findContours(edged.copy(), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    # 初始化标志位，表示是否找到有效的棋盘
    valid_board = False
    # 初始化最大轮廓对象
    max_contour = None
    
    # 查找最大的轮廓作为棋盘
    if contours:
        # 遍历所有轮廓
        for contour in contours:
            # 计算当前轮廓的面积
            area = cv2.contourArea(contour)
            # 如果轮廓面积大于最小面积限制
            if area > MIN_BOARD_AREA:
                # 如果最大轮廓对象为空或者当前轮廓面积更大
                if max_contour is None or area > cv2.contourArea(max_contour):
                    # 更新最大轮廓对象
                    max_contour = contour

        if max_contour is not None:
            # 计算最大轮廓的周长
            perimeter = cv2.arcLength(max_contour, True)
            # 对最大轮廓进行多边形逼近，得到近似的四边形
            approx = cv2.approxPolyDP(max_contour, 0.02 * perimeter, True)

            # 判断近似的多边形是否为四边形
            if len(approx) == 4:
                # 标记找到有效的棋盘
                valid_board = True
                # 获取四边形的四个顶点坐标
                pts = approx.reshape(4, 2)
                
                # 计算顶点坐标的和
                s = pts.sum(axis=1)
                # 创建一个形状为 (4, 2) 的零数组，用于存储排序后的顶点坐标
                rect = np.zeros((4, 2), dtype="float32")
                # 左上角顶点的坐标和最小，将其赋值给 rect[0]
                rect[0] = pts[np.argmin(s)]
                # 右下角顶点的坐标和最大，将其赋值给 rect[2]
                rect[2] = pts[np.argmax(s)]
                # 计算每个顶点坐标的差值
                diff = np.diff(pts, axis=1)
                # 右上角顶点的坐标差值最小，将其赋值给 rect[1]
                rect[1] = pts[np.argmin(diff)]
                # 左下角顶点的坐标差值最大，将其赋值给 rect[3]
                rect[3] = pts[np.argmax(diff)]
                
                # 解包排序后的顶点坐标
                (tl, tr, br, bl) = rect
                # 定义目标棋盘的宽度和高度
                width = 300
                height = 300
                # 定义目标棋盘的四个顶点坐标
                dst_rect = np.array([
                    [0, 0],
                    [width, 0],
                    [width, height],
                    [0, height]
                ], dtype="float32")

                # 检查四边形的边长是否大于 0，确保是有效的四边形
                if np.linalg.norm(tl - tr) > 0 and np.linalg.norm(tr - br) > 0 and np.linalg.norm(br - bl) > 0 and np.linalg.norm(bl - tl) > 0:
                    # 计算透视变换矩阵，用于将原始四边形变换到目标矩形
                    M = cv2.getPerspectiveTransform(rect, dst_rect)
                    # 初始化存储棋格中心点坐标的列表
                    grid_centers = []

                    # 遍历 3x3 的棋格
                    for i in range(3):
                        for j in range(3):
                            # 计算当前棋格中心点在目标矩形中的 x 坐标
                            cx = int((j + 0.5) * (width / 3))
                            # 计算当前棋格中心点在目标矩形中的 y 坐标
                            cy = int((i + 0.5) * (height / 3))
                            # 将棋格中心点坐标添加到列表中
                            grid_centers.append((cx, cy))

                    # 计算透视变换矩阵的逆矩阵，用于将目标矩形中的点变换回原始图像
                    inv_M = np.linalg.inv(M)
                    # 初始化存储棋格中心点在原始图像中坐标的列表
                    original_centers = []

                    # 遍历目标矩形中的棋格中心点坐标
                    for (cx, cy) in grid_centers:
                        # 将棋格中心点坐标转换为齐次坐标
                        original_pt = np.array([cx, cy, 1], dtype="float32").reshape(-1, 1)
                        # 使用逆矩阵将目标矩形中的点变换回原始图像
                        original_pt = np.dot(inv_M, original_pt)
                        # 进行归一化处理
                        original_pt = original_pt / original_pt[2]
                        # 将变换后的坐标转换为整数，并添加到列表中
                        original_centers.append((int(original_pt[0].item()), int(original_pt[1].item())))

                    # 初始化存储颜色类别的列表
                    color_categories = []
                    # 遍历棋格中心点坐标
                    for (cx, cy) in original_centers:
                        # 获取当前棋格中心点的颜色
                        color = gray[cy, cx]
                        # 如果灰度值大于 200，判断为白色棋子
                        if color > 200:
                            color_categories.append(2)
                        # 如果灰度值小于 50，判断为黑色棋子
                        elif color < 50:
                            color_categories.append(1)
                        # 其他情况判断为无棋子
                        else:
                            color_categories.append(0)

                    # 初始化 3x3 的棋盘列表，用于存储棋盘信息
                    board = [[0 for _ in range(3)] for _ in range(3)]
                    # 初始化索引
                    index = 0
                    # 遍历 3x3 的棋格
                    for i in range(3):
                        for j in range(3):
                            # 如果索引在颜色类别列表的有效范围内
                            if index < len(color_categories):
                                # 将颜色类别赋值给棋盘列表
                                board[i][j] = color_categories[index]
                            # 索引加 1
                            index += 1

                    # 打印棋盘信息
                    for row in board:
                        print('|'.join(map(str, row)))
                    print('-' * 5)

                    # 在图像上绘制棋盘轮廓
                    cv2.drawContours(img, [approx], -1, (0, 255, 0), 2)

                    # 初始化索引
                    index = 0
                    # 遍历棋格中心点坐标
                    for center in original_centers:
                        # 解包棋格中心点坐标
                        x, y = center
                        # 如果索引在颜色类别列表的有效范围内
                        if index < len(color_categories):
                            # 根据颜色类别设置文本内容
                            if color_categories[index] == 2:
                                text = '2'
                            elif color_categories[index] == 1:
                                text = '1'
                            else:
                                text = '0'

                            # 在图像上绘制绿色的棋子种类数字
                            cv2.putText(img, text, (x - 5, y + 5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
                        # 索引加 1
                        index += 1

    # 计算处理时间
    process_time = (time.time() - start_time) * 1000
    print(f"处理时间: {process_time:.2f} ms")

    # 显示处理后的图像
    cv2.imshow('Board Detection', img)
    
    # 按 'q' 键退出
    if cv2.waitKey(1) & 0xFF == ord('q'):
        break

# 释放摄像头资源并关闭所有窗口
cap.release()
cv2.destroyAllWindows()