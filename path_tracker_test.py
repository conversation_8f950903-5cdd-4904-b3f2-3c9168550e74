import cv2
import numpy as np
import math

# 路径追踪参数
MIN_LINE_LENGTH = 30     # 最小直线长度
CURVATURE_THRESHOLD = 0.05 # 曲率阈值，用于区分直线和弧线
ARC_MIN_RADIUS = 10      # 最小弧线半径
ARC_MAX_RADIUS = 200     # 最大弧线半径

def calculate_curvature(points, index, window=3):
    """计算轮廓点的曲率"""
    if len(points) < window * 2 + 1:
        return 0
    
    start_idx = max(0, index - window)
    end_idx = min(len(points), index + window + 1)
    
    if end_idx - start_idx < 3:
        return 0
    
    # 取前后几个点计算曲率
    p1 = points[start_idx][0]
    p2 = points[index][0]
    p3 = points[end_idx-1][0]
    
    # 计算向量
    v1 = np.array([p2[0] - p1[0], p2[1] - p1[1]], dtype=float)
    v2 = np.array([p3[0] - p2[0], p3[1] - p2[1]], dtype=float)
    
    # 避免除零
    norm1 = np.linalg.norm(v1)
    norm2 = np.linalg.norm(v2)
    
    if norm1 < 1e-6 or norm2 < 1e-6:
        return 0
    
    # 计算夹角
    cos_angle = np.dot(v1, v2) / (norm1 * norm2)
    cos_angle = np.clip(cos_angle, -1, 1)
    angle = np.arccos(cos_angle)
    
    # 曲率 = 角度变化 / 弧长
    arc_length = (norm1 + norm2) / 2
    if arc_length < 1e-6:
        return 0
    
    return angle / arc_length

def segment_contour(contour):
    """将轮廓分割为直线段和弧线段"""
    if len(contour) < 10:
        return []
    
    segments = []
    
    # 计算所有点的曲率
    curvatures = []
    for i in range(len(contour)):
        curvature = calculate_curvature(contour, i)
        curvatures.append(curvature)
    
    # 使用滑动窗口平滑曲率
    smoothed_curvatures = []
    window = 3
    for i in range(len(curvatures)):
        start = max(0, i - window)
        end = min(len(curvatures), i + window + 1)
        avg_curvature = np.mean(curvatures[start:end])
        smoothed_curvatures.append(avg_curvature)
    
    # 根据曲率分段
    current_segment = {'type': None, 'points': [], 'start_idx': 0}
    
    for i, curvature in enumerate(smoothed_curvatures):
        is_straight = curvature < CURVATURE_THRESHOLD
        
        if current_segment['type'] is None:
            current_segment['type'] = 'line' if is_straight else 'arc'
            current_segment['points'].append(contour[i][0])
            current_segment['start_idx'] = i
        elif (current_segment['type'] == 'line' and is_straight) or \
             (current_segment['type'] == 'arc' and not is_straight):
            # 继续当前段
            current_segment['points'].append(contour[i][0])
        else:
            # 开始新段
            if len(current_segment['points']) > 8:  # 最小段长度
                segments.append(current_segment.copy())
            
            current_segment = {
                'type': 'line' if is_straight else 'arc',
                'points': [contour[i][0]],
                'start_idx': i
            }
    
    # 添加最后一段
    if len(current_segment['points']) > 8:
        segments.append(current_segment)
    
    return segments

def extract_line_keypoints(points):
    """提取直线段的关键点"""
    if len(points) < 2:
        return None
    
    start_point = tuple(map(int, points[0]))
    end_point = tuple(map(int, points[-1]))
    
    # 计算直线长度
    length = np.sqrt((end_point[0] - start_point[0])**2 + (end_point[1] - start_point[1])**2)
    
    if length < MIN_LINE_LENGTH:
        return None
    
    # 计算角度
    angle = math.atan2(end_point[1] - start_point[1], end_point[0] - start_point[0])
    angle_deg = math.degrees(angle)
    
    return {
        'type': 'line',
        'start': start_point,
        'end': end_point,
        'length': length,
        'angle': angle_deg
    }

def extract_arc_keypoints(points):
    """提取弧线段的关键点"""
    if len(points) < 5:
        return None
    
    # 使用三点法拟合圆
    try:
        # 选择三个代表性点
        p1 = np.array(points[0], dtype=float)
        p2 = np.array(points[len(points)//2], dtype=float)
        p3 = np.array(points[-1], dtype=float)
        
        # 计算圆心
        ax, ay = p1
        bx, by = p2
        cx, cy = p3
        
        d = 2 * (ax * (by - cy) + bx * (cy - ay) + cx * (ay - by))
        if abs(d) < 1e-6:
            return None
        
        ux = ((ax*ax + ay*ay) * (by - cy) + (bx*bx + by*by) * (cy - ay) + (cx*cx + cy*cy) * (ay - by)) / d
        uy = ((ax*ax + ay*ay) * (cx - bx) + (bx*bx + by*by) * (ax - cx) + (cx*cx + cy*cy) * (bx - ax)) / d
        
        center = (int(ux), int(uy))
        radius = np.sqrt((ux - ax)**2 + (uy - ay)**2)
        
        # 检查半径是否合理
        if radius < ARC_MIN_RADIUS or radius > ARC_MAX_RADIUS:
            return None
        
        start_point = tuple(map(int, points[0]))
        end_point = tuple(map(int, points[-1]))
        mid_point = tuple(map(int, points[len(points)//2]))
        
        return {
            'type': 'arc',
            'start': start_point,
            'end': end_point,
            'mid': mid_point,
            'center': center,
            'radius': radius
        }
    
    except:
        return None

# 初始化摄像头
cap = cv2.VideoCapture(0)
cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)

print("路径追踪程序已启动...")
print("按 'q' 键退出程序")
print("按 's' 键保存当前帧")

frame_count = 0

while True:
    frame_count += 1
    
    # 读取图像
    ret, img = cap.read()
    if not ret:
        print("无法读取摄像头画面")
        break
    
    # 创建显示用的图像副本
    img_display = img.copy()
    
    # 获取图像尺寸
    height, width = img.shape[:2]
    center_x, center_y = width // 2, height // 2
    
    # 绘制图像中心点
    cv2.line(img_display, (center_x - 10, center_y), (center_x + 10, center_y), (255, 0, 0), 2)
    cv2.line(img_display, (center_x, center_y - 10), (center_x, center_y + 10), (255, 0, 0), 2)
    
    # 转换为灰度图
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    
    # 针对白底黑线优化预处理
    blurred = cv2.GaussianBlur(gray, (3, 3), 0)
    
    # 二值化处理 - 白底黑线
    _, binary = cv2.threshold(blurred, 127, 255, cv2.THRESH_BINARY_INV)
    
    # 形态学操作 - 连接断开的线条
    kernel = np.ones((3,3), np.uint8)
    binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
    binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)
    
    # 查找轮廓
    contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)
    
    # 寻找最长的轮廓作为主路径
    if contours:
        main_contour = max(contours, key=len)
        
        if len(main_contour) > 30:
            # 绘制原始轮廓
            cv2.drawContours(img_display, [main_contour], -1, (128, 128, 128), 1)
            
            # 分割轮廓
            segments = segment_contour(main_contour)
            
            print(f"\n=== Frame {frame_count} ===")
            print(f"轮廓长度: {len(main_contour)}, 检测到 {len(segments)} 个路径段")
            
            # 处理每个段
            for i, segment in enumerate(segments):
                if segment['type'] == 'line':
                    keypoints = extract_line_keypoints(segment['points'])
                    if keypoints:
                        # 绘制直线段
                        start = keypoints['start']
                        end = keypoints['end']
                        cv2.line(img_display, start, end, (255, 0, 0), 3)
                        
                        # 绘制关键点
                        cv2.circle(img_display, start, 6, (0, 0, 255), -1)
                        cv2.circle(img_display, end, 6, (0, 0, 255), -1)
                        
                        # 标注
                        mid_x = (start[0] + end[0]) // 2
                        mid_y = (start[1] + end[1]) // 2
                        cv2.putText(img_display, f"L{i}", (mid_x, mid_y-10), 
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)
                        
                        print(f"  直线段 {i}: ({start[0]},{start[1]}) -> ({end[0]},{end[1]})")
                        print(f"    长度: {keypoints['length']:.1f}, 角度: {keypoints['angle']:.1f}°")
                
                elif segment['type'] == 'arc':
                    keypoints = extract_arc_keypoints(segment['points'])
                    if keypoints:
                        # 绘制弧线段
                        start = keypoints['start']
                        end = keypoints['end']
                        mid = keypoints['mid']
                        center = keypoints['center']
                        
                        # 绘制弧线轮廓
                        points = np.array(segment['points'], np.int32)
                        cv2.polylines(img_display, [points], False, (0, 255, 255), 3)
                        
                        # 绘制关键点
                        cv2.circle(img_display, start, 6, (0, 0, 255), -1)
                        cv2.circle(img_display, end, 6, (0, 0, 255), -1)
                        cv2.circle(img_display, mid, 6, (255, 0, 255), -1)
                        cv2.circle(img_display, center, 4, (0, 255, 0), -1)
                        
                        # 绘制半径线
                        cv2.line(img_display, center, start, (0, 255, 0), 1)
                        cv2.line(img_display, center, end, (0, 255, 0), 1)
                        
                        # 标注
                        cv2.putText(img_display, f"A{i}", (mid[0], mid[1]-10), 
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)
                        
                        print(f"  弧线段 {i}: ({start[0]},{start[1]}) -> ({end[0]},{end[1]})")
                        print(f"    中点({mid[0]},{mid[1]}), 圆心({center[0]},{center[1]})")
                        print(f"    半径: {keypoints['radius']:.1f}")
    
    # 显示处理后的二值图像（小窗口）
    binary_small = cv2.resize(binary, (160, 120))
    img_display[10:130, width-170:width-10] = cv2.cvtColor(binary_small, cv2.COLOR_GRAY2BGR)
    
    # 显示图像
    cv2.imshow('Path Tracker', img_display)
    
    # 检查按键
    key = cv2.waitKey(1) & 0xFF
    if key == ord('q'):
        break
    elif key == ord('s'):
        cv2.imwrite(f'path_frame_{frame_count}.jpg', img_display)
        print(f"保存帧 {frame_count}")

# 释放资源
cap.release()
cv2.destroyAllWindows()
