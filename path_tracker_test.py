import cv2
import numpy as np
import math

# 路径追踪参数
MIN_LINE_LENGTH = 30     # 最小直线长度
CURVATURE_THRESHOLD = 0.05 # 曲率阈值，用于区分直线和弧线
ARC_MIN_RADIUS = 10      # 最小弧线半径
ARC_MAX_RADIUS = 200     # 最大弧线半径

def calculate_curvature_advanced(points, index, k=5):
    """
    改进的曲率计算：前后k个邻点拟合直线求夹角
    按照用户需求：取某点前后k个邻点，拟合直线L1和L2，计算夹角
    """
    if len(points) < k * 2 + 1 or index < k or index >= len(points) - k:
        return 0

    # 取前后k个邻点
    p_prev = points[index-k:index]  # 前k个点
    p_next = points[index:index+k]  # 后k个点（包含当前点）

    if len(p_prev) < 3 or len(p_next) < 3:
        return 0

    try:
        # 拟合前段直线L1
        x_prev = np.array([p[0] for p in p_prev], dtype=float)
        y_prev = np.array([p[1] for p in p_prev], dtype=float)

        # 使用最小二乘法拟合直线 y = ax + b
        A_prev = np.vstack([x_prev, np.ones(len(x_prev))]).T
        coeffs_prev = np.linalg.lstsq(A_prev, y_prev, rcond=None)[0]
        a1 = coeffs_prev[0]

        # 拟合后段直线L2
        x_next = np.array([p[0] for p in p_next], dtype=float)
        y_next = np.array([p[1] for p in p_next], dtype=float)

        A_next = np.vstack([x_next, np.ones(len(x_next))]).T
        coeffs_next = np.linalg.lstsq(A_next, y_next, rcond=None)[0]
        a2 = coeffs_next[0]

        # 计算两直线的方向向量
        v1 = np.array([1, a1])  # 直线L1的方向向量
        v2 = np.array([1, a2])  # 直线L2的方向向量

        # 计算夹角
        cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
        cos_angle = np.clip(cos_angle, -1, 1)
        angle = np.arccos(cos_angle)

        # 返回角度（弧度），角度越大说明曲率越大
        return angle

    except:
        return 0

def fit_line_error(points):
    """计算点集拟合直线的平均误差"""
    if len(points) < 2:
        return float('inf')

    try:
        x = np.array([p[0] for p in points], dtype=float)
        y = np.array([p[1] for p in points], dtype=float)

        # 拟合直线 y = ax + b
        A = np.vstack([x, np.ones(len(x))]).T
        coeffs = np.linalg.lstsq(A, y, rcond=None)[0]
        a, b = coeffs

        # 计算每个点到直线的距离
        distances = np.abs(a * x - y + b) / np.sqrt(a**2 + 1)
        return np.mean(distances)
    except:
        return float('inf')

def fit_circle_error(points):
    """计算点集拟合圆的平均误差"""
    if len(points) < 3:
        return float('inf')

    try:
        x = np.array([p[0] for p in points], dtype=float)
        y = np.array([p[1] for p in points], dtype=float)

        # 圆的一般方程: x² + y² + Dx + Ey + F = 0
        # 转换为: Dx + Ey + F = -(x² + y²)
        A = np.column_stack([x, y, np.ones(len(x))])
        b = -(x**2 + y**2)

        coeffs = np.linalg.lstsq(A, b, rcond=None)[0]
        D, E, F = coeffs

        # 圆心和半径
        center_x = -D / 2
        center_y = -E / 2
        radius = np.sqrt(center_x**2 + center_y**2 - F)

        # 计算每个点到圆的距离误差
        distances_to_center = np.sqrt((x - center_x)**2 + (y - center_y)**2)
        errors = np.abs(distances_to_center - radius)
        return np.mean(errors)
    except:
        return float('inf')

def detect_feature_points(contour, curvature_threshold=30, window_size=20):
    """
    检测关键特征点：拐点 + 形状切换点
    按照用户需求实现两种突变点检测
    """
    if len(contour) < window_size * 2:
        return []

    feature_points = []

    # === 1. 检测拐点（曲率突变点） ===
    curvatures = []
    for i in range(len(contour)):
        curvature = calculate_curvature_advanced(contour, i, k=5)
        curvatures.append(math.degrees(curvature))  # 转换为角度

    # 检测曲率突变点
    for i in range(1, len(curvatures) - 1):
        if curvatures[i] > curvature_threshold:  # 超过阈值的点为拐点
            feature_points.append({
                'index': i,
                'type': 'curvature_point',
                'value': curvatures[i],
                'point': tuple(contour[i])
            })

    # === 2. 检测形状切换点（滑动窗口拟合误差分析） ===
    shape_types = []  # 记录每个窗口的最优形状类型

    # 滑动窗口分析
    for i in range(0, len(contour) - window_size, window_size // 2):
        window_points = contour[i:i + window_size]

        # 计算直线和圆弧拟合误差
        line_error = fit_line_error(window_points)
        circle_error = fit_circle_error(window_points)

        # 判断最优形状（误差更小的为最优）
        if line_error < circle_error:
            shape_types.append(('line', i, line_error))
        else:
            shape_types.append(('circle', i, circle_error))

    # 检测形状切换点
    for i in range(1, len(shape_types)):
        prev_shape, prev_idx, prev_error = shape_types[i-1]
        curr_shape, curr_idx, curr_error = shape_types[i]

        # 当相邻窗口的最优形状不同时，交界点为形状切换点
        if prev_shape != curr_shape:
            switch_idx = (prev_idx + curr_idx) // 2
            if switch_idx < len(contour):
                feature_points.append({
                    'index': switch_idx,
                    'type': 'shape_switch_point',
                    'value': f"{prev_shape}->{curr_shape}",
                    'point': tuple(contour[switch_idx])
                })

    # 按索引排序并去重
    feature_points.sort(key=lambda x: x['index'])

    # 去除距离过近的特征点
    filtered_points = []
    min_distance = 10  # 最小距离阈值

    for point in feature_points:
        if not filtered_points:
            filtered_points.append(point)
        else:
            last_point = filtered_points[-1]
            distance = np.sqrt((point['point'][0] - last_point['point'][0])**2 +
                             (point['point'][1] - last_point['point'][1])**2)
            if distance > min_distance:
                filtered_points.append(point)

    return filtered_points

def segment_contour(contour):
    """将轮廓分割为直线段和弧线段"""
    if len(contour) < 10:
        return []
    
    segments = []
    
    # 计算所有点的曲率
    curvatures = []
    for i in range(len(contour)):
        curvature = calculate_curvature_advanced(contour, i)
        curvatures.append(curvature)
    
    # 使用滑动窗口平滑曲率
    smoothed_curvatures = []
    window = 3
    for i in range(len(curvatures)):
        start = max(0, i - window)
        end = min(len(curvatures), i + window + 1)
        avg_curvature = np.mean(curvatures[start:end])
        smoothed_curvatures.append(avg_curvature)
    
    # 根据曲率分段
    current_segment = {'type': None, 'points': [], 'start_idx': 0}
    
    for i, curvature in enumerate(smoothed_curvatures):
        is_straight = curvature < CURVATURE_THRESHOLD
        
        if current_segment['type'] is None:
            current_segment['type'] = 'line' if is_straight else 'arc'
            current_segment['points'].append(contour[i][0])
            current_segment['start_idx'] = i
        elif (current_segment['type'] == 'line' and is_straight) or \
             (current_segment['type'] == 'arc' and not is_straight):
            # 继续当前段
            current_segment['points'].append(contour[i][0])
        else:
            # 开始新段
            if len(current_segment['points']) > 8:  # 最小段长度
                segments.append(current_segment.copy())
            
            current_segment = {
                'type': 'line' if is_straight else 'arc',
                'points': [contour[i][0]],
                'start_idx': i
            }
    
    # 添加最后一段
    if len(current_segment['points']) > 8:
        segments.append(current_segment)
    
    return segments

def extract_line_keypoints(points):
    """提取直线段的关键点"""
    if len(points) < 2:
        return None
    
    start_point = tuple(map(int, points[0]))
    end_point = tuple(map(int, points[-1]))
    
    # 计算直线长度
    length = np.sqrt((end_point[0] - start_point[0])**2 + (end_point[1] - start_point[1])**2)
    
    if length < MIN_LINE_LENGTH:
        return None
    
    # 计算角度
    angle = math.atan2(end_point[1] - start_point[1], end_point[0] - start_point[0])
    angle_deg = math.degrees(angle)
    
    return {
        'type': 'line',
        'start': start_point,
        'end': end_point,
        'length': length,
        'angle': angle_deg
    }

def extract_arc_keypoints(points):
    """提取弧线段的关键点"""
    if len(points) < 5:
        return None
    
    # 使用三点法拟合圆
    try:
        # 选择三个代表性点
        p1 = np.array(points[0], dtype=float)
        p2 = np.array(points[len(points)//2], dtype=float)
        p3 = np.array(points[-1], dtype=float)
        
        # 计算圆心
        ax, ay = p1
        bx, by = p2
        cx, cy = p3
        
        d = 2 * (ax * (by - cy) + bx * (cy - ay) + cx * (ay - by))
        if abs(d) < 1e-6:
            return None
        
        ux = ((ax*ax + ay*ay) * (by - cy) + (bx*bx + by*by) * (cy - ay) + (cx*cx + cy*cy) * (ay - by)) / d
        uy = ((ax*ax + ay*ay) * (cx - bx) + (bx*bx + by*by) * (ax - cx) + (cx*cx + cy*cy) * (bx - ax)) / d
        
        center = (int(ux), int(uy))
        radius = np.sqrt((ux - ax)**2 + (uy - ay)**2)
        
        # 检查半径是否合理
        if radius < ARC_MIN_RADIUS or radius > ARC_MAX_RADIUS:
            return None
        
        start_point = tuple(map(int, points[0]))
        end_point = tuple(map(int, points[-1]))
        mid_point = tuple(map(int, points[len(points)//2]))
        
        return {
            'type': 'arc',
            'start': start_point,
            'end': end_point,
            'mid': mid_point,
            'center': center,
            'radius': radius
        }
    
    except:
        return None

# 初始化摄像头
cap = cv2.VideoCapture(0)
cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)

print("路径追踪程序已启动...")
print("按 'q' 键退出程序")
print("按 's' 键保存当前帧")

frame_count = 0

while True:
    frame_count += 1
    
    # 读取图像
    ret, img = cap.read()
    if not ret:
        print("无法读取摄像头画面")
        break
    
    # 创建显示用的图像副本
    img_display = img.copy()
    
    # 获取图像尺寸
    height, width = img.shape[:2]
    center_x, center_y = width // 2, height // 2
    
    # 绘制图像中心点
    cv2.line(img_display, (center_x - 10, center_y), (center_x + 10, center_y), (255, 0, 0), 2)
    cv2.line(img_display, (center_x, center_y - 10), (center_x, center_y + 10), (255, 0, 0), 2)
    
    # === 步骤1：图像预处理（确保轮廓清晰） ===

    # 1. 灰度化
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

    # 2. 去噪处理
    # 高斯模糊去除微小噪点
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    # 中值滤波去除椒盐噪声（如果有毛边）
    denoised = cv2.medianBlur(blurred, 3)

    # 3. 二值化处理 - 白底黑线
    # 方案A：固定阈值（适合背景简单的情况）
    _, binary_fixed = cv2.threshold(denoised, 127, 255, cv2.THRESH_BINARY_INV)

    # 方案B：自适应阈值（适合有轻微光影的情况）
    binary_adaptive = cv2.adaptiveThreshold(denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                          cv2.THRESH_BINARY_INV, 11, 2)

    # 选择更好的二值化结果（可以通过轮廓数量和质量判断）
    contours_fixed, _ = cv2.findContours(binary_fixed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    contours_adaptive, _ = cv2.findContours(binary_adaptive, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # 选择轮廓更清晰的版本（轮廓数量少且主轮廓长度大）
    if len(contours_fixed) > 0 and len(contours_adaptive) > 0:
        max_len_fixed = max(len(c) for c in contours_fixed)
        max_len_adaptive = max(len(c) for c in contours_adaptive)
        # 优先选择主轮廓更长且轮廓总数较少的版本
        if max_len_fixed > max_len_adaptive * 0.8 and len(contours_fixed) <= len(contours_adaptive):
            binary = binary_fixed
        else:
            binary = binary_adaptive
    else:
        binary = binary_fixed  # 默认使用固定阈值

    # 4. 形态学操作 - 确保轮廓连续性
    kernel = np.ones((3, 3), np.uint8)
    # 闭运算：连接断开的线条
    binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
    # 开运算：去除小噪点
    binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel, iterations=1)
    
    # === 步骤2：提取连续轮廓（获取形状的"一笔画"路径） ===

    # 1. 提取轮廓 - 保留所有轮廓点，确保不丢失细节
    contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)

    # 2. 筛选主轮廓 - 选择面积最大的轮廓（排除噪声）
    if contours:
        # 按轮廓面积排序，选择最大的
        contours_with_area = [(cv2.contourArea(c), c) for c in contours]
        contours_with_area.sort(key=lambda x: x[0], reverse=True)

        # 选择面积最大且长度足够的轮廓
        main_contour = None
        for area, contour in contours_with_area:
            if len(contour) > 30 and area > 100:  # 最小面积和长度要求
                main_contour = contour
                break

        if main_contour is not None:
            # 3. 规整轮廓点 - 转换为 (N,2) 格式
            main_contour = main_contour.reshape(-1, 2)

            print(f"主轮廓信息: 点数={len(main_contour)}, 面积={cv2.contourArea(main_contour.reshape(-1,1,2)):.1f}")

            # === 步骤3：检测关键特征点 ===
            feature_points = detect_feature_points(main_contour)

            # === 步骤4：轮廓分段与端点提取 ===
            # 收集所有关键坐标点（包括起点、终点、特征点）
            key_points = [{'index': 0, 'type': 'start_point', 'point': tuple(main_contour[0])}]
            key_points.extend(feature_points)
            key_points.append({'index': len(main_contour)-1, 'type': 'end_point', 'point': tuple(main_contour[-1])})

            # 按轮廓顺序排序
            key_points.sort(key=lambda x: x['index'])

            # 分段：相邻两个关键点之间为一段
            segments = []
            for i in range(len(key_points) - 1):
                start_idx = key_points[i]['index']
                end_idx = key_points[i + 1]['index']

                if end_idx > start_idx:
                    segment_points = main_contour[start_idx:end_idx + 1]

                    # 判断段的形状类型
                    line_error = fit_line_error(segment_points)
                    circle_error = fit_circle_error(segment_points)
                    shape_type = 'line' if line_error < circle_error else 'arc'

                    segments.append({
                        'start_point': tuple(main_contour[start_idx]),
                        'end_point': tuple(main_contour[end_idx]),
                        'points': segment_points,
                        'type': shape_type,
                        'start_idx': start_idx,
                        'end_idx': end_idx
                    })

            print(f"\n=== Frame {frame_count} ===")
            print(f"轮廓长度: {len(main_contour)}")
            print(f"检测到 {len(feature_points)} 个特征点")
            print(f"分割为 {len(segments)} 个路径段")
            
            # 绘制原始轮廓
            cv2.drawContours(img_display, [main_contour.reshape(-1,1,2)], -1, (128, 128, 128), 1)

            # === 步骤5：输出关键坐标点 ===
            laser_control_data = []

            # 绘制和输出每个段的信息
            for i, segment in enumerate(segments):
                start_point = segment['start_point']
                end_point = segment['end_point']
                segment_type = segment['type']

                if segment_type == 'line':
                    # 直线段处理
                    length = np.sqrt((end_point[0] - start_point[0])**2 + (end_point[1] - start_point[1])**2)
                    angle = math.degrees(math.atan2(end_point[1] - start_point[1], end_point[0] - start_point[0]))

                    # 绘制直线段
                    cv2.line(img_display, start_point, end_point, (255, 0, 0), 3)
                    cv2.circle(img_display, start_point, 6, (0, 0, 255), -1)
                    cv2.circle(img_display, end_point, 6, (0, 0, 255), -1)

                    # 标注
                    mid_x = (start_point[0] + end_point[0]) // 2
                    mid_y = (start_point[1] + end_point[1]) // 2
                    cv2.putText(img_display, f"L{i}", (mid_x, mid_y-10),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)

                    # 激光控制数据
                    laser_data = {
                        'segment_id': i,
                        'type': 'line',
                        'start': start_point,
                        'end': end_point,
                        'length': length,
                        'angle': angle
                    }
                    laser_control_data.append(laser_data)

                    print(f"  直线段 {i}: ({start_point[0]},{start_point[1]}) -> ({end_point[0]},{end_point[1]})")
                    print(f"    长度: {length:.1f}, 角度: {angle:.1f}°")

                elif segment_type == 'arc':
                    # 弧线段处理
                    arc_keypoints = extract_arc_keypoints(segment['points'])
                    if arc_keypoints:
                        center = arc_keypoints['center']
                        radius = arc_keypoints['radius']
                        mid_point = arc_keypoints['mid']

                        # 绘制弧线段
                        points = np.array(segment['points'], np.int32)
                        cv2.polylines(img_display, [points], False, (0, 255, 255), 3)
                        cv2.circle(img_display, start_point, 6, (0, 0, 255), -1)
                        cv2.circle(img_display, end_point, 6, (0, 0, 255), -1)
                        cv2.circle(img_display, mid_point, 6, (255, 0, 255), -1)
                        cv2.circle(img_display, center, 4, (0, 255, 0), -1)

                        # 绘制半径线
                        cv2.line(img_display, center, start_point, (0, 255, 0), 1)
                        cv2.line(img_display, center, end_point, (0, 255, 0), 1)

                        # 标注
                        cv2.putText(img_display, f"A{i}", (mid_point[0], mid_point[1]-10),
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)

                        # 激光控制数据
                        laser_data = {
                            'segment_id': i,
                            'type': 'arc',
                            'start': start_point,
                            'end': end_point,
                            'mid': mid_point,
                            'center': center,
                            'radius': radius
                        }
                        laser_control_data.append(laser_data)

                        print(f"  弧线段 {i}: ({start_point[0]},{start_point[1]}) -> ({end_point[0]},{end_point[1]})")
                        print(f"    中点({mid_point[0]},{mid_point[1]}), 圆心({center[0]},{center[1]})")
                        print(f"    半径: {radius:.1f}")

            # 绘制特征点
            for fp in feature_points:
                point = fp['point']
                if fp['type'] == 'curvature_point':
                    cv2.circle(img_display, point, 8, (255, 255, 0), 2)  # 黄色圆圈：拐点
                    cv2.putText(img_display, "C", (point[0]+10, point[1]),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 0), 1)
                elif fp['type'] == 'shape_switch_point':
                    cv2.circle(img_display, point, 8, (255, 0, 255), 2)  # 紫色圆圈：形状切换点
                    cv2.putText(img_display, "S", (point[0]+10, point[1]),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 0, 255), 1)

            # 输出完整的激光控制数据
            if laser_control_data:
                print(f"\n=== 激光控制数据 ===")
                for data in laser_control_data:
                    print(f"段{data['segment_id']}: {data}")
                print("=" * 50)
    
    # 显示处理后的二值图像（小窗口）
    binary_small = cv2.resize(binary, (160, 120))
    img_display[10:130, width-170:width-10] = cv2.cvtColor(binary_small, cv2.COLOR_GRAY2BGR)
    
    # 显示图像
    cv2.imshow('Path Tracker', img_display)
    
    # 检查按键
    key = cv2.waitKey(1) & 0xFF
    if key == ord('q'):
        break
    elif key == ord('s'):
        cv2.imwrite(f'path_frame_{frame_count}.jpg', img_display)
        print(f"保存帧 {frame_count}")

# 释放资源
cap.release()
cv2.destroyAllWindows()
