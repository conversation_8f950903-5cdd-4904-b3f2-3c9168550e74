import cv2
import numpy as np
import math

class ROIContourAnalyzer:
    def __init__(self):
        # ROI检测参数
        self.min_rect_area = 1000      # 矩形最小面积
        self.max_rect_area = 50000     # 矩形最大面积
        self.rect_aspect_ratio_min = 0.3  # 矩形最小宽高比
        self.rect_aspect_ratio_max = 3.0  # 矩形最大宽高比
        
        # 轮廓分析参数
        self.min_contour_area = 200    # 最小轮廓面积（过滤小噪声）
        self.max_contour_area = 20000  # 最大轮廓面积
        
        # 图像处理参数
        self.gaussian_kernel = (5, 5)  # 高斯模糊核大小
        self.canny_low = 50           # Canny边缘检测低阈值
        self.canny_high = 150         # Canny边缘检测高阈值
        
    def preprocess_image(self, img):
        """图像预处理：灰度化、去噪、边缘检测"""
        # 灰度化
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # 高斯模糊去噪
        blurred = cv2.GaussianBlur(gray, self.gaussian_kernel, 0)
        
        # Canny边缘检测
        edges = cv2.Canny(blurred, self.canny_low, self.canny_high)
        
        return gray, blurred, edges
    
    def detect_rectangles(self, edges):
        """检测矩形ROI区域"""
        # 查找轮廓
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        rectangles = []
        
        for contour in contours:
            # 计算轮廓面积
            area = cv2.contourArea(contour)
            
            # 面积过滤
            if area < self.min_rect_area or area > self.max_rect_area:
                continue
            
            # 多边形逼近
            epsilon = 0.02 * cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, epsilon, True)
            
            # 检查是否为四边形（矩形）
            if len(approx) == 4:
                # 计算边界矩形
                x, y, w, h = cv2.boundingRect(approx)
                
                # 检查宽高比
                aspect_ratio = float(w) / h
                if self.rect_aspect_ratio_min <= aspect_ratio <= self.rect_aspect_ratio_max:
                    rectangles.append({
                        'contour': contour,
                        'approx': approx,
                        'bbox': (x, y, w, h),
                        'area': area,
                        'aspect_ratio': aspect_ratio
                    })
        
        # 按面积排序，返回最大的矩形作为主ROI
        rectangles.sort(key=lambda x: x['area'], reverse=True)
        return rectangles
    
    def analyze_contours_in_roi(self, img, roi_bbox):
        """分析ROI区域内的轮廓"""
        x, y, w, h = roi_bbox
        
        # 提取ROI区域
        roi_img = img[y:y+h, x:x+w]
        
        # 对ROI区域进行预处理
        gray_roi, blurred_roi, edges_roi = self.preprocess_image(roi_img)
        
        # 查找ROI内的轮廓
        contours, _ = cv2.findContours(edges_roi, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)
        
        # 过滤轮廓
        filtered_contours = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if self.min_contour_area <= area <= self.max_contour_area:
                # 将ROI内的坐标转换为原图坐标
                contour_global = contour + np.array([x, y])
                filtered_contours.append({
                    'contour': contour_global,
                    'contour_local': contour,
                    'area': area,
                    'perimeter': cv2.arcLength(contour, True)
                })
        
        # 按面积排序
        filtered_contours.sort(key=lambda x: x['area'], reverse=True)
        
        return {
            'roi_img': roi_img,
            'gray_roi': gray_roi,
            'edges_roi': edges_roi,
            'contours': filtered_contours
        }
    
    def visualize_results(self, img, rectangles, roi_analysis=None):
        """可视化检测结果"""
        result_img = img.copy()
        
        # 绘制检测到的矩形
        for i, rect in enumerate(rectangles):
            # 绘制矩形轮廓
            cv2.drawContours(result_img, [rect['contour']], -1, (0, 255, 0), 2)
            
            # 绘制边界框
            x, y, w, h = rect['bbox']
            cv2.rectangle(result_img, (x, y), (x+w, y+h), (255, 0, 0), 2)
            
            # 标注信息
            cv2.putText(result_img, f"ROI {i}", (x, y-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 0), 2)
            cv2.putText(result_img, f"Area: {rect['area']:.0f}", (x, y+h+20), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)
            cv2.putText(result_img, f"Ratio: {rect['aspect_ratio']:.2f}", (x, y+h+40), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)
        
        # 如果有ROI分析结果，绘制内部轮廓
        if roi_analysis and len(rectangles) > 0:
            contours = roi_analysis['contours']
            
            # 绘制ROI内的轮廓
            for i, contour_info in enumerate(contours):
                contour = contour_info['contour']
                
                # 不同颜色绘制不同轮廓
                colors = [(0, 0, 255), (255, 255, 0), (255, 0, 255), (0, 255, 255)]
                color = colors[i % len(colors)]
                
                cv2.drawContours(result_img, [contour], -1, color, 2)
                
                # 计算轮廓中心点
                M = cv2.moments(contour)
                if M["m00"] != 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])
                    
                    # 标注轮廓信息
                    cv2.circle(result_img, (cx, cy), 5, color, -1)
                    cv2.putText(result_img, f"C{i}", (cx+10, cy), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
                    cv2.putText(result_img, f"A:{contour_info['area']:.0f}", 
                               (cx+10, cy+20), cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
        
        return result_img

def process_image(image_path):
    """处理单张图像"""
    # 读取图像
    img = cv2.imread(image_path)
    if img is None:
        print(f"无法读取图像: {image_path}")
        return
    
    print(f"处理图像: {image_path}")
    print(f"图像尺寸: {img.shape}")
    
    # 创建分析器
    analyzer = ROIContourAnalyzer()
    
    # 图像预处理
    gray, blurred, edges = analyzer.preprocess_image(img)
    
    # 检测矩形ROI
    rectangles = analyzer.detect_rectangles(edges)
    print(f"检测到 {len(rectangles)} 个矩形ROI")
    
    roi_analysis = None
    if len(rectangles) > 0:
        # 分析主ROI（面积最大的矩形）内的轮廓
        main_roi = rectangles[0]
        roi_analysis = analyzer.analyze_contours_in_roi(img, main_roi['bbox'])
        print(f"主ROI内检测到 {len(roi_analysis['contours'])} 个轮廓")
        
        # 打印轮廓信息
        for i, contour_info in enumerate(roi_analysis['contours']):
            print(f"  轮廓 {i}: 面积={contour_info['area']:.1f}, 周长={contour_info['perimeter']:.1f}")
    
    # 可视化结果
    result_img = analyzer.visualize_results(img, rectangles, roi_analysis)
    
    # 显示结果
    cv2.imshow('Original', cv2.resize(img, (800, 600)))
    cv2.imshow('Edges', cv2.resize(edges, (800, 600)))
    cv2.imshow('Result', cv2.resize(result_img, (800, 600)))
    
    # 如果有ROI分析结果，显示ROI区域
    if roi_analysis:
        cv2.imshow('ROI', cv2.resize(roi_analysis['roi_img'], (400, 300)))
        cv2.imshow('ROI Edges', cv2.resize(roi_analysis['edges_roi'], (400, 300)))
    
    # 保存结果
    output_path = image_path.replace('.', '_analyzed.')
    cv2.imwrite(output_path, result_img)
    print(f"结果已保存到: {output_path}")
    
    cv2.waitKey(0)
    cv2.destroyAllWindows()

def process_camera():
    """处理摄像头实时视频"""
    cap = cv2.VideoCapture(0)
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    
    analyzer = ROIContourAnalyzer()
    
    print("摄像头模式启动...")
    print("按键说明:")
    print("  'q': 退出")
    print("  's': 保存当前帧")
    print("  '+': 增加最小轮廓面积")
    print("  '-': 减少最小轮廓面积")
    
    frame_count = 0
    
    while True:
        ret, img = cap.read()
        if not ret:
            break
        
        frame_count += 1
        
        # 图像预处理
        gray, blurred, edges = analyzer.preprocess_image(img)
        
        # 检测矩形ROI
        rectangles = analyzer.detect_rectangles(edges)
        
        roi_analysis = None
        if len(rectangles) > 0:
            # 分析主ROI内的轮廓
            main_roi = rectangles[0]
            roi_analysis = analyzer.analyze_contours_in_roi(img, main_roi['bbox'])
        
        # 可视化结果
        result_img = analyzer.visualize_results(img, rectangles, roi_analysis)
        
        # 显示参数信息
        cv2.putText(result_img, f"Min Area: {analyzer.min_contour_area}", 
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        cv2.putText(result_img, f"ROIs: {len(rectangles)}", 
                   (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        if roi_analysis:
            cv2.putText(result_img, f"Contours: {len(roi_analysis['contours'])}", 
                       (10, 90), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        cv2.imshow('ROI Contour Analyzer', result_img)
        
        # 按键处理
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break
        elif key == ord('s'):
            filename = f'roi_analysis_frame_{frame_count}.jpg'
            cv2.imwrite(filename, result_img)
            print(f"保存帧: {filename}")
        elif key == ord('+') or key == ord('='):
            analyzer.min_contour_area += 50
            print(f"最小轮廓面积: {analyzer.min_contour_area}")
        elif key == ord('-'):
            analyzer.min_contour_area = max(50, analyzer.min_contour_area - 50)
            print(f"最小轮廓面积: {analyzer.min_contour_area}")
    
    cap.release()
    cv2.destroyAllWindows()

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        # 处理指定图像
        image_path = sys.argv[1]
        process_image(image_path)
    else:
        # 摄像头模式
        process_camera()
