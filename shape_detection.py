import cv2
import numpy as np
import time

# 轮廓面积限制变量
MIN_CONTOUR_AREA = 200   # 最小轮廓面积（降低以检测更多细节）
MAX_CONTOUR_AREA = 20000 # 最大轮廓面积

# ROI矩形检测参数
MIN_RECT_AREA = 1000      # 矩形最小面积
MAX_RECT_AREA = 50000     # 矩形最大面积
RECT_ASPECT_RATIO_MIN = 0.3  # 矩形最小宽高比
RECT_ASPECT_RATIO_MAX = 3.0  # 矩形最大宽高比

# 调试显示相关变量
show_debug_windows = True  # 是否显示调试窗口
show_edges = True         # 是否显示边缘检测结果
show_contours = True      # 是否显示轮廓检测结果
auto_detect_roi = True    # 是否自动检测ROI矩形

# ROI相关变量
roi_selected = False
roi_start_point = None
roi_end_point = None
roi_rect = None  # (x, y, w, h)
selecting_roi = False

# 鼠标回调函数
def mouse_callback(event, x, y, flags, param):
    global roi_start_point, roi_end_point, roi_selected, selecting_roi, roi_rect

    if event == cv2.EVENT_LBUTTONDOWN:
        # 开始选择ROI
        roi_start_point = (x, y)
        roi_end_point = (x, y)
        selecting_roi = True
        roi_selected = False

    elif event == cv2.EVENT_MOUSEMOVE and selecting_roi:
        # 拖拽过程中更新终点
        roi_end_point = (x, y)

    elif event == cv2.EVENT_LBUTTONUP:
        # 完成ROI选择
        if selecting_roi:
            roi_end_point = (x, y)
            selecting_roi = False

            # 计算ROI矩形
            x1, y1 = roi_start_point
            x2, y2 = roi_end_point

            # 确保坐标正确（左上角和右下角）
            roi_x = min(x1, x2)
            roi_y = min(y1, y2)
            roi_w = abs(x2 - x1)
            roi_h = abs(y2 - y1)

            # 只有当ROI有一定大小时才认为有效
            if roi_w > 20 and roi_h > 20:
                roi_rect = (roi_x, roi_y, roi_w, roi_h)
                roi_selected = True
                print(f"ROI区域已选择: ({roi_x}, {roi_y}, {roi_w}, {roi_h})")
            else:
                roi_selected = False
                roi_rect = None
                print("ROI区域太小，已取消选择")

# 初始化摄像头 (使用默认摄像头，通常是0)
cap = cv2.VideoCapture(0)
cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)

print("形状检测程序已启动...")
print(f"轮廓面积范围: {MIN_CONTOUR_AREA} - {MAX_CONTOUR_AREA}")
print("操作说明:")
print("- 鼠标左键拖拽选择ROI区域")
print("- 按 'r' 键重置ROI区域（全帧检测）")
print("- 按 'e' 键切换边缘检测窗口显示")
print("- 按 'c' 键切换轮廓检测窗口显示")
print("- 按 'd' 键切换所有调试窗口显示")
print("- 按 'q' 键退出程序")

# 设置鼠标回调
cv2.namedWindow('Shape Detection')
cv2.setMouseCallback('Shape Detection', mouse_callback)

# 创建调试窗口
if show_debug_windows:
    if show_edges:
        cv2.namedWindow('Edges', cv2.WINDOW_NORMAL)
        cv2.resizeWindow('Edges', 400, 300)
    if show_contours:
        cv2.namedWindow('Contours', cv2.WINDOW_NORMAL)
        cv2.resizeWindow('Contours', 400, 300)

frame_count = 0

while True:
    frame_count += 1

    # 读取图像
    ret, img = cap.read()
    if not ret:
        print("无法读取摄像头画面")
        break

    # 创建显示用的图像副本
    img_display = img.copy()

    # 获取图像尺寸
    height, width = img.shape[:2]
    center_x, center_y = width // 2, height // 2

    # 绘制图像中心点
    cv2.line(img_display, (center_x - 10, center_y), (center_x + 10, center_y), (255, 0, 0), 2)
    cv2.line(img_display, (center_x, center_y - 10), (center_x, center_y + 10), (255, 0, 0), 2)
    cv2.circle(img_display, (center_x, center_y), 3, (255, 0, 0), -1)

    # 确定处理区域
    if roi_selected and roi_rect is not None:
        # 使用ROI区域
        roi_x, roi_y, roi_w, roi_h = roi_rect
        # 确保ROI在图像范围内
        roi_x = max(0, min(roi_x, width - 1))
        roi_y = max(0, min(roi_y, height - 1))
        roi_w = min(roi_w, width - roi_x)
        roi_h = min(roi_h, height - roi_y)

        # 提取ROI区域
        img_roi = img[roi_y:roi_y+roi_h, roi_x:roi_x+roi_w]

        # 绘制ROI边框
        cv2.rectangle(img_display, (roi_x, roi_y), (roi_x + roi_w, roi_y + roi_h), (0, 255, 255), 2)
        cv2.putText(img_display, "ROI", (roi_x, roi_y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)

        # 转换为灰度图
        gray = cv2.cvtColor(img_roi, cv2.COLOR_BGR2GRAY)
    else:
        # 使用全帧
        img_roi = img
        roi_x, roi_y = 0, 0
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

    # 高斯模糊去噪
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)

    # 边缘检测
    edges = cv2.Canny(blurred, 50, 150)

    # 查找轮廓
    contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # 创建轮廓可视化图像
    if show_debug_windows and show_contours:
        # 创建彩色轮廓图像
        contour_img = cv2.cvtColor(gray, cv2.COLOR_GRAY2BGR)
        # 绘制所有轮廓（不同颜色）
        for i, contour in enumerate(contours):
            # 使用不同颜色绘制轮廓
            color = ((i * 50) % 255, (i * 80) % 255, (i * 120) % 255)
            cv2.drawContours(contour_img, [contour], -1, color, 2)

            # 显示轮廓面积
            area = cv2.contourArea(contour)
            if area > 100:  # 只显示较大轮廓的面积
                M = cv2.moments(contour)
                if M["m00"] != 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])
                    cv2.putText(contour_img, f"{int(area)}", (cx-20, cy),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)

    # 统计检测到的形状
    shape_count = {}

    # 遍历所有轮廓
    for i, contour in enumerate(contours):
        # 调整轮廓坐标到全图坐标系（如果使用了ROI）
        if roi_selected and roi_rect is not None:
            # 将ROI内的轮廓坐标转换为全图坐标
            contour_adjusted = contour.copy()
            contour_adjusted[:, :, 0] += roi_x  # x坐标偏移
            contour_adjusted[:, :, 1] += roi_y  # y坐标偏移
        else:
            contour_adjusted = contour

        # 检测形状
        # 计算轮廓周长
        perimeter = cv2.arcLength(contour, True)

        # 多边形逼近
        approx = cv2.approxPolyDP(contour, 0.02 * perimeter, True)

        # 计算轮廓面积
        area = cv2.contourArea(contour)

        # 如果轮廓面积超出范围，忽略
        if area < MIN_CONTOUR_AREA or area > MAX_CONTOUR_AREA:
            shape_name = "Small"
        else:
            # 根据顶点数量判断形状
            vertices = len(approx)

            if vertices == 3:
                shape_name = "three-side"
            elif vertices == 4:
                # 计算长宽比来区分矩形和正方形
                x, y, w, h = cv2.boundingRect(approx)
                aspect_ratio = float(w) / h
                if 0.9 <= aspect_ratio <= 1.1:
                    shape_name = "four-side-square"
                else:
                    shape_name = "four-side"
            elif vertices == 5:
                shape_name = "five-side"
            elif vertices == 6:
                shape_name = "six-side"
            elif vertices == 7:
                shape_name = "seven-side"
            elif vertices >= 8:
                shape_name = "circle"
            else:
                shape_name = f"{vertices}-side"

        # 跳过小图形
        if shape_name == "Small":
            continue

        # 统计形状数量
        if shape_name in shape_count:
            shape_count[shape_name] += 1
        else:
            shape_count[shape_name] = 1

        # 调整多边形逼近坐标到全图坐标系
        if roi_selected and roi_rect is not None:
            approx_adjusted = approx.copy()
            approx_adjusted[:, :, 0] += roi_x
            approx_adjusted[:, :, 1] += roi_y
        else:
            approx_adjusted = approx

        # 绘制轮廓（使用调整后的坐标）
        cv2.drawContours(img_display, [contour_adjusted], -1, (0, 255, 0), 2)

        # 绘制多边形逼近结果（使用调整后的坐标）
        cv2.drawContours(img_display, [approx_adjusted], -1, (255, 0, 255), 2)

        # 计算轮廓中心点
        M = cv2.moments(contour)
        if M["m00"] != 0:
            cx = int(M["m10"] / M["m00"])
            cy = int(M["m01"] / M["m00"])

            # 调整中心点坐标到全图坐标系
            if roi_selected and roi_rect is not None:
                cx_display = cx + roi_x
                cy_display = cy + roi_y
            else:
                cx_display = cx
                cy_display = cy

            # 在形状中心绘制文本
            cv2.putText(img_display, shape_name, (cx_display - 20, cy_display),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)

            # 绘制形状中心点
            cv2.circle(img_display, (cx_display, cy_display), 3, (0, 0, 255), -1)

            # 串口打印检测结果（使用全图坐标）
            print(f"{shape_name} ({cx_display},{cy_display}) 轮廓面积: {int(area)}")
    
    # 绘制正在选择的ROI
    if selecting_roi and roi_start_point is not None and roi_end_point is not None:
        x1, y1 = roi_start_point
        x2, y2 = roi_end_point
        cv2.rectangle(img_display, (x1, y1), (x2, y2), (0, 255, 255), 2)
        cv2.putText(img_display, "Selecting ROI...", (x1, y1 - 10),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)

    # 在图像顶部显示统计信息
    y_offset = 20
    cv2.putText(img_display, f"Frame: {frame_count}",
               (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)

    y_offset += 15
    cv2.putText(img_display, f"Image Center: ({center_x}, {center_y})",
               (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)

    y_offset += 15
    # 显示ROI状态
    if roi_selected and roi_rect is not None:
        roi_x, roi_y, roi_w, roi_h = roi_rect
        cv2.putText(img_display, f"ROI: ({roi_x}, {roi_y}, {roi_w}, {roi_h})",
                   (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 255), 1)
    else:
        cv2.putText(img_display, "ROI: Full Frame",
                   (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)

    y_offset += 15
    cv2.putText(img_display, f"Area Range: {MIN_CONTOUR_AREA}-{MAX_CONTOUR_AREA}",
               (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)

    y_offset += 15
    cv2.putText(img_display, "Controls: Mouse=ROI, R=Reset, E=Edges, C=Contours, D=Debug, Q=Quit",
               (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.35, (255, 255, 0), 1)

    y_offset += 20
    for shape, count in shape_count.items():
        cv2.putText(img_display, f"{shape}: {count}",
                   (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
        y_offset += 15

    # 显示主窗口
    cv2.imshow('Shape Detection', img_display)

    # 显示调试窗口
    if show_debug_windows:
        if show_edges:
            cv2.imshow('Edges', edges)
        if show_contours and 'contour_img' in locals():
            cv2.imshow('Contours', contour_img)

    # 检查按键
    key = cv2.waitKey(1) & 0xFF
    if key == ord('q'):
        break
    elif key == ord('r') or key == ord('R'):
        # 重置ROI区域
        roi_selected = False
        roi_rect = None
        roi_start_point = None
        roi_end_point = None
        selecting_roi = False
        print("ROI区域已重置，恢复全帧检测")
    elif key == ord('e') or key == ord('E'):
        # 切换边缘检测窗口
        show_edges = not show_edges
        if show_edges:
            cv2.namedWindow('Edges', cv2.WINDOW_NORMAL)
            cv2.resizeWindow('Edges', 400, 300)
            print("边缘检测窗口已开启")
        else:
            cv2.destroyWindow('Edges')
            print("边缘检测窗口已关闭")
    elif key == ord('c') or key == ord('C'):
        # 切换轮廓检测窗口
        show_contours = not show_contours
        if show_contours:
            cv2.namedWindow('Contours', cv2.WINDOW_NORMAL)
            cv2.resizeWindow('Contours', 400, 300)
            print("轮廓检测窗口已开启")
        else:
            cv2.destroyWindow('Contours')
            print("轮廓检测窗口已关闭")
    elif key == ord('d') or key == ord('D'):
        # 切换所有调试窗口
        show_debug_windows = not show_debug_windows
        if show_debug_windows:
            if show_edges:
                cv2.namedWindow('Edges', cv2.WINDOW_NORMAL)
                cv2.resizeWindow('Edges', 400, 300)
            if show_contours:
                cv2.namedWindow('Contours', cv2.WINDOW_NORMAL)
                cv2.resizeWindow('Contours', 400, 300)
            print("调试窗口已开启")
        else:
            cv2.destroyWindow('Edges')
            cv2.destroyWindow('Contours')
            print("调试窗口已关闭")

# 释放资源
cap.release()
cv2.destroyAllWindows()
