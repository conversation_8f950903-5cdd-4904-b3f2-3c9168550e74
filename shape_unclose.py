import cv2
import numpy as np
import time
import math

# 轮廓面积限制变量
MIN_CONTOUR_AREA = 500   # 最小轮廓面积
MAX_CONTOUR_AREA = 20000 # 最大轮廓面积

# 路径追踪参数
MIN_LINE_LENGTH = 30     # 最小直线长度
MAX_LINE_GAP = 10        # 直线间最大间隙
CURVATURE_THRESHOLD = 0.1 # 曲率阈值，用于区分直线和弧线
ARC_MIN_RADIUS = 10      # 最小弧线半径
ARC_MAX_RADIUS = 200     # 最大弧线半径

def calculate_curvature(points, index, window=5):
    """计算轮廓点的曲率"""
    if len(points) < window * 2 + 1:
        return 0

    start_idx = max(0, index - window)
    end_idx = min(len(points), index + window + 1)

    if end_idx - start_idx < 3:
        return 0

    # 取前后几个点计算曲率
    p1 = points[start_idx][0]
    p2 = points[index][0]
    p3 = points[end_idx-1][0]

    # 计算向量
    v1 = np.array([p2[0] - p1[0], p2[1] - p1[1]])
    v2 = np.array([p3[0] - p2[0], p3[1] - p2[1]])

    # 避免除零
    norm1 = np.linalg.norm(v1)
    norm2 = np.linalg.norm(v2)

    if norm1 == 0 or norm2 == 0:
        return 0

    # 计算夹角
    cos_angle = np.dot(v1, v2) / (norm1 * norm2)
    cos_angle = np.clip(cos_angle, -1, 1)
    angle = np.arccos(cos_angle)

    # 曲率 = 角度变化 / 弧长
    arc_length = norm1 + norm2
    if arc_length == 0:
        return 0

    return angle / arc_length

def segment_contour(contour):
    """将轮廓分割为直线段和弧线段"""
    if len(contour) < 10:
        return []

    segments = []
    current_segment = {'type': None, 'points': [], 'start_idx': 0}

    curvatures = []
    for i in range(len(contour)):
        curvature = calculate_curvature(contour, i)
        curvatures.append(curvature)

    # 根据曲率分段
    for i, curvature in enumerate(curvatures):
        is_straight = curvature < CURVATURE_THRESHOLD

        if current_segment['type'] is None:
            current_segment['type'] = 'line' if is_straight else 'arc'
            current_segment['points'].append(contour[i][0])
            current_segment['start_idx'] = i
        elif (current_segment['type'] == 'line' and is_straight) or \
             (current_segment['type'] == 'arc' and not is_straight):
            # 继续当前段
            current_segment['points'].append(contour[i][0])
        else:
            # 开始新段
            if len(current_segment['points']) > 5:  # 最小段长度
                segments.append(current_segment.copy())

            current_segment = {
                'type': 'line' if is_straight else 'arc',
                'points': [contour[i][0]],
                'start_idx': i
            }

    # 添加最后一段
    if len(current_segment['points']) > 5:
        segments.append(current_segment)

    return segments

def extract_line_keypoints(points):
    """提取直线段的关键点"""
    if len(points) < 2:
        return None

    start_point = points[0]
    end_point = points[-1]

    # 计算直线长度
    length = np.sqrt((end_point[0] - start_point[0])**2 + (end_point[1] - start_point[1])**2)

    # 计算角度
    angle = math.atan2(end_point[1] - start_point[1], end_point[0] - start_point[0])
    angle_deg = math.degrees(angle)

    return {
        'type': 'line',
        'start': start_point,
        'end': end_point,
        'length': length,
        'angle': angle_deg
    }

def extract_arc_keypoints(points):
    """提取弧线段的关键点"""
    if len(points) < 3:
        return None

    # 使用最小二乘法拟合圆
    points_array = np.array(points)
    x = points_array[:, 0]
    y = points_array[:, 1]

    # 圆的一般方程: (x-a)² + (y-b)² = r²
    # 展开: x² + y² - 2ax - 2by + (a² + b² - r²) = 0
    # 设 A = -2a, B = -2b, C = a² + b² - r²
    # 则: x² + y² + Ax + By + C = 0

    try:
        # 构建矩阵方程
        A_matrix = np.column_stack([x, y, np.ones(len(x))])
        b_vector = -(x**2 + y**2)

        # 求解
        coeffs = np.linalg.lstsq(A_matrix, b_vector, rcond=None)[0]
        A, B, C = coeffs

        # 计算圆心和半径
        center_x = -A / 2
        center_y = -B / 2
        radius = np.sqrt(center_x**2 + center_y**2 - C)

        # 检查半径是否合理
        if radius < ARC_MIN_RADIUS or radius > ARC_MAX_RADIUS:
            return None

        start_point = points[0]
        end_point = points[-1]
        mid_idx = len(points) // 2
        mid_point = points[mid_idx]

        return {
            'type': 'arc',
            'start': start_point,
            'end': end_point,
            'mid': mid_point,
            'center': (int(center_x), int(center_y)),
            'radius': radius
        }

    except:
        return None

# 初始化摄像头 (使用默认摄像头，通常是0)
cap = cv2.VideoCapture(0)
cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)

print("形状检测程序已启动...")
print(f"轮廓面积范围: {MIN_CONTOUR_AREA} - {MAX_CONTOUR_AREA}")
print("按 'q' 键退出程序")

frame_count = 0

while True:
    frame_count += 1

    # 读取图像
    ret, img = cap.read()
    if not ret:
        print("无法读取摄像头画面")
        break

    # 创建显示用的图像副本
    img_display = img.copy()

    # 获取图像尺寸
    height, width = img.shape[:2]
    center_x, center_y = width // 2, height // 2
    
    # 绘制图像中心点
    cv2.line(img_display, (center_x - 10, center_y), (center_x + 10, center_y), (255, 0, 0), 2)
    cv2.line(img_display, (center_x, center_y - 10), (center_x, center_y + 10), (255, 0, 0), 2)
    cv2.circle(img_display, (center_x, center_y), 3, (255, 0, 0), -1)
    
    # 转换为灰度图
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

    # 针对白底黑线优化预处理
    # 1. 高斯模糊去噪
    blurred = cv2.GaussianBlur(gray, (3, 3), 0)

    # 2. 二值化处理 - 白底黑线
    _, binary = cv2.threshold(blurred, 127, 255, cv2.THRESH_BINARY_INV)

    # 3. 形态学操作 - 连接断开的线条
    kernel = np.ones((3,3), np.uint8)
    binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)

    # 4. 骨架化 - 提取线条中心线 (如果ximgproc可用)
    try:
        skeleton = cv2.ximgproc.thinning(binary)
    except AttributeError:
        # 如果ximgproc不可用，使用形态学操作近似
        kernel = np.ones((3,3), np.uint8)
        skeleton = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)
        skeleton = cv2.morphologyEx(skeleton, cv2.MORPH_CLOSE, kernel)

    # 边缘检测（备用方案）
    edges = cv2.Canny(blurred, 50, 150)
    
    # 查找轮廓 - 优先使用骨架化结果
    contours_skeleton, _ = cv2.findContours(skeleton, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)
    contours_edges, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # 选择更好的轮廓
    contours = contours_skeleton if len(contours_skeleton) > 0 else contours_edges

    # 统计检测到的路径段
    path_segments = []

    # 遍历所有轮廓，寻找最长的路径
    max_length = 0
    main_contour = None

    for contour in contours:
        if len(contour) > max_length:
            max_length = len(contour)
            main_contour = contour

    if main_contour is not None and len(main_contour) > 20:
        # 绘制主轮廓
        cv2.drawContours(img_display, [main_contour], -1, (0, 255, 0), 2)

        # 分割轮廓为直线段和弧线段
        segments = segment_contour(main_contour)

        print(f"\n=== Frame {frame_count} ===")
        print(f"检测到 {len(segments)} 个路径段:")

        # 处理每个段
        for i, segment in enumerate(segments):
            if segment['type'] == 'line':
                keypoints = extract_line_keypoints(segment['points'])
                if keypoints:
                    path_segments.append(keypoints)

                    # 绘制直线段
                    start = keypoints['start']
                    end = keypoints['end']
                    cv2.line(img_display, start, end, (255, 0, 0), 3)

                    # 绘制关键点
                    cv2.circle(img_display, start, 5, (0, 0, 255), -1)
                    cv2.circle(img_display, end, 5, (0, 0, 255), -1)

                    # 标注信息
                    mid_x = (start[0] + end[0]) // 2
                    mid_y = (start[1] + end[1]) // 2
                    cv2.putText(img_display, f"L{i}", (mid_x, mid_y),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 2)

                    print(f"  直线段 {i}: 起点({start[0]},{start[1]}) -> 终点({end[0]},{end[1]})")
                    print(f"    长度: {keypoints['length']:.1f}, 角度: {keypoints['angle']:.1f}°")

            elif segment['type'] == 'arc':
                keypoints = extract_arc_keypoints(segment['points'])
                if keypoints:
                    path_segments.append(keypoints)

                    # 绘制弧线段
                    start = keypoints['start']
                    end = keypoints['end']
                    mid = keypoints['mid']
                    center = keypoints['center']
                    radius = keypoints['radius']

                    # 绘制弧线轮廓
                    points = np.array(segment['points'], np.int32)
                    cv2.polylines(img_display, [points], False, (0, 255, 255), 3)

                    # 绘制关键点
                    cv2.circle(img_display, start, 5, (0, 0, 255), -1)
                    cv2.circle(img_display, end, 5, (0, 0, 255), -1)
                    cv2.circle(img_display, mid, 5, (255, 0, 255), -1)
                    cv2.circle(img_display, center, 3, (0, 255, 0), -1)

                    # 绘制半径线
                    cv2.line(img_display, center, start, (0, 255, 0), 1)
                    cv2.line(img_display, center, end, (0, 255, 0), 1)

                    # 标注信息
                    cv2.putText(img_display, f"A{i}", mid,
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 2)

                    print(f"  弧线段 {i}: 起点({start[0]},{start[1]}) -> 终点({end[0]},{end[1]})")
                    print(f"    中点({mid[0]},{mid[1]}), 圆心({center[0]},{center[1]})")
                    print(f"    半径: {radius:.1f}")
    
    # 在图像顶部显示统计信息
    y_offset = 20
    cv2.putText(img_display, f"Frame: {frame_count}", 
               (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
    
    y_offset += 15
    cv2.putText(img_display, f"Image Center: ({center_x}, {center_y})", 
               (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
    
    y_offset += 15
    cv2.putText(img_display, f"Area Range: {MIN_CONTOUR_AREA}-{MAX_CONTOUR_AREA}", 
               (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
    
    y_offset += 15

    # 显示路径段统计
    line_count = sum(1 for seg in path_segments if seg['type'] == 'line')
    arc_count = sum(1 for seg in path_segments if seg['type'] == 'arc')

    y_offset += 5
    cv2.putText(img_display, f"Path Segments: {len(path_segments)}",
               (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
    y_offset += 15
    cv2.putText(img_display, f"Lines: {line_count}, Arcs: {arc_count}",
               (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
    y_offset += 15
    
    # 显示图像
    cv2.imshow('Shape Detection', img_display)

    # 检查按键退出
    key = cv2.waitKey(1) & 0xFF
    if key == ord('q'):
        break

# 释放资源
cap.release()
cv2.destroyAllWindows()
